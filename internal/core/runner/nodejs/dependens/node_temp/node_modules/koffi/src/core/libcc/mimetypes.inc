// Copyright 2023 Ni<PERSON> <<EMAIL>>
//
// Permission is hereby granted, free of charge, to any person obtaining a copy of
// this software and associated documentation files (the “Software”), to deal in 
// the Software without restriction, including without limitation the rights to use,
// copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the
// Software, and to permit persons to whom the Software is furnished to do so,
// subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,
// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
// OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
// NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
// HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
// OTHER DEALINGS IN THE SOFTWARE.

#ifndef MIMETYPE
    #error Please define MIMETYPE() before including mimetypes.inc
#endif

MIMETYPE(".123", "application/vnd.lotus-1-2-3")
MIMETYPE(".1km", "application/vnd.1000minds.decision-model+xml")
MIMETYPE(".3dml", "text/vnd.in3d.3dml")
MIMETYPE(".3ds", "image/x-3ds")
MIMETYPE(".3g2", "video/3gpp2")
MIMETYPE(".3gp", "video/3gpp")
MIMETYPE(".3gpp", "audio/3gpp")
MIMETYPE(".3mf", "model/3mf")
MIMETYPE(".7z", "application/x-7z-compressed")
MIMETYPE(".aab", "application/x-authorware-bin")
MIMETYPE(".aac", "audio/aac")
MIMETYPE(".aam", "application/x-authorware-map")
MIMETYPE(".aas", "application/x-authorware-seg")
MIMETYPE(".abw", "application/x-abiword")
MIMETYPE(".ac", "application/pkix-attr-cert")
MIMETYPE(".acc", "application/vnd.americandynamics.acc")
MIMETYPE(".ace", "application/x-ace-compressed")
MIMETYPE(".acu", "application/vnd.acucobol")
MIMETYPE(".acutc", "application/vnd.acucorp")
MIMETYPE(".adp", "audio/adpcm")
MIMETYPE(".adts", "audio/aac")
MIMETYPE(".aep", "application/vnd.audiograph")
MIMETYPE(".afm", "application/x-font-type1")
MIMETYPE(".afp", "application/vnd.ibm.modcap")
MIMETYPE(".age", "application/vnd.age")
MIMETYPE(".ahead", "application/vnd.ahead.space")
MIMETYPE(".ai", "application/postscript")
MIMETYPE(".aif", "audio/x-aiff")
MIMETYPE(".aifc", "audio/x-aiff")
MIMETYPE(".aiff", "audio/x-aiff")
MIMETYPE(".air", "application/vnd.adobe.air-application-installer-package+zip")
MIMETYPE(".ait", "application/vnd.dvb.ait")
MIMETYPE(".ami", "application/vnd.amiga.ami")
MIMETYPE(".aml", "application/automationml-aml+xml")
MIMETYPE(".amlx", "application/automationml-amlx+zip")
MIMETYPE(".amr", "audio/amr")
MIMETYPE(".apk", "application/vnd.android.package-archive")
MIMETYPE(".apng", "image/apng")
MIMETYPE(".appcache", "text/cache-manifest")
MIMETYPE(".appinstaller", "application/appinstaller")
MIMETYPE(".application", "application/x-ms-application")
MIMETYPE(".appx", "application/appx")
MIMETYPE(".appxbundle", "application/appxbundle")
MIMETYPE(".apr", "application/vnd.lotus-approach")
MIMETYPE(".arc", "application/x-freearc")
MIMETYPE(".arj", "application/x-arj")
MIMETYPE(".asc", "application/pgp-keys")
MIMETYPE(".asf", "video/x-ms-asf")
MIMETYPE(".asm", "text/x-asm")
MIMETYPE(".aso", "application/vnd.accpac.simply.aso")
MIMETYPE(".asx", "video/x-ms-asf")
MIMETYPE(".atc", "application/vnd.acucorp")
MIMETYPE(".atom", "application/atom+xml")
MIMETYPE(".atomcat", "application/atomcat+xml")
MIMETYPE(".atomdeleted", "application/atomdeleted+xml")
MIMETYPE(".atomsvc", "application/atomsvc+xml")
MIMETYPE(".atx", "application/vnd.antix.game-component")
MIMETYPE(".au", "audio/basic")
MIMETYPE(".avci", "image/avci")
MIMETYPE(".avcs", "image/avcs")
MIMETYPE(".avi", "video/x-msvideo")
MIMETYPE(".avif", "image/avif")
MIMETYPE(".aw", "application/applixware")
MIMETYPE(".azf", "application/vnd.airzip.filesecure.azf")
MIMETYPE(".azs", "application/vnd.airzip.filesecure.azs")
MIMETYPE(".azv", "image/vnd.airzip.accelerator.azv")
MIMETYPE(".azw", "application/vnd.amazon.ebook")
MIMETYPE(".b16", "image/vnd.pco.b16")
MIMETYPE(".bat", "application/x-msdownload")
MIMETYPE(".bcpio", "application/x-bcpio")
MIMETYPE(".bdf", "application/x-font-bdf")
MIMETYPE(".bdm", "application/vnd.syncml.dm+wbxml")
MIMETYPE(".bdoc", "application/bdoc")
MIMETYPE(".bed", "application/vnd.realvnc.bed")
MIMETYPE(".bh2", "application/vnd.fujitsu.oasysprs")
MIMETYPE(".bin", "application/octet-stream")
MIMETYPE(".blb", "application/x-blorb")
MIMETYPE(".blorb", "application/x-blorb")
MIMETYPE(".bmi", "application/vnd.bmi")
MIMETYPE(".bmml", "application/vnd.balsamiq.bmml+xml")
MIMETYPE(".bmp", "image/bmp")
MIMETYPE(".book", "application/vnd.framemaker")
MIMETYPE(".box", "application/vnd.previewsystems.box")
MIMETYPE(".boz", "application/x-bzip2")
MIMETYPE(".bpk", "application/octet-stream")
MIMETYPE(".bsp", "model/vnd.valve.source.compiled-map")
MIMETYPE(".btf", "image/prs.btif")
MIMETYPE(".btif", "image/prs.btif")
MIMETYPE(".buffer", "application/octet-stream")
MIMETYPE(".bz", "application/x-bzip")
MIMETYPE(".bz2", "application/x-bzip2")
MIMETYPE(".c", "text/x-c")
MIMETYPE(".c11amc", "application/vnd.cluetrust.cartomobile-config")
MIMETYPE(".c11amz", "application/vnd.cluetrust.cartomobile-config-pkg")
MIMETYPE(".c4d", "application/vnd.clonk.c4group")
MIMETYPE(".c4f", "application/vnd.clonk.c4group")
MIMETYPE(".c4g", "application/vnd.clonk.c4group")
MIMETYPE(".c4p", "application/vnd.clonk.c4group")
MIMETYPE(".c4u", "application/vnd.clonk.c4group")
MIMETYPE(".cab", "application/vnd.ms-cab-compressed")
MIMETYPE(".caf", "audio/x-caf")
MIMETYPE(".cap", "application/vnd.tcpdump.pcap")
MIMETYPE(".car", "application/vnd.curl.car")
MIMETYPE(".cat", "application/vnd.ms-pki.seccat")
MIMETYPE(".cb7", "application/x-cbr")
MIMETYPE(".cba", "application/x-cbr")
MIMETYPE(".cbr", "application/x-cbr")
MIMETYPE(".cbt", "application/x-cbr")
MIMETYPE(".cbz", "application/x-cbr")
MIMETYPE(".cc", "text/x-c")
MIMETYPE(".cco", "application/x-cocoa")
MIMETYPE(".cct", "application/x-director")
MIMETYPE(".ccxml", "application/ccxml+xml")
MIMETYPE(".cdbcmsg", "application/vnd.contact.cmsg")
MIMETYPE(".cdf", "application/x-netcdf")
MIMETYPE(".cdfx", "application/cdfx+xml")
MIMETYPE(".cdkey", "application/vnd.mediastation.cdkey")
MIMETYPE(".cdmia", "application/cdmi-capability")
MIMETYPE(".cdmic", "application/cdmi-container")
MIMETYPE(".cdmid", "application/cdmi-domain")
MIMETYPE(".cdmio", "application/cdmi-object")
MIMETYPE(".cdmiq", "application/cdmi-queue")
MIMETYPE(".cdx", "chemical/x-cdx")
MIMETYPE(".cdxml", "application/vnd.chemdraw+xml")
MIMETYPE(".cdy", "application/vnd.cinderella")
MIMETYPE(".cer", "application/pkix-cert")
MIMETYPE(".cfs", "application/x-cfs-compressed")
MIMETYPE(".cgm", "image/cgm")
MIMETYPE(".chat", "application/x-chat")
MIMETYPE(".chm", "application/vnd.ms-htmlhelp")
MIMETYPE(".chrt", "application/vnd.kde.kchart")
MIMETYPE(".cif", "chemical/x-cif")
MIMETYPE(".cii", "application/vnd.anser-web-certificate-issue-initiation")
MIMETYPE(".cil", "application/vnd.ms-artgalry")
MIMETYPE(".cjs", "application/node")
MIMETYPE(".cla", "application/vnd.claymore")
MIMETYPE(".class", "application/java-vm")
MIMETYPE(".cld", "model/vnd.cld")
MIMETYPE(".clkk", "application/vnd.crick.clicker.keyboard")
MIMETYPE(".clkp", "application/vnd.crick.clicker.palette")
MIMETYPE(".clkt", "application/vnd.crick.clicker.template")
MIMETYPE(".clkw", "application/vnd.crick.clicker.wordbank")
MIMETYPE(".clkx", "application/vnd.crick.clicker")
MIMETYPE(".clp", "application/x-msclip")
MIMETYPE(".cmc", "application/vnd.cosmocaller")
MIMETYPE(".cmdf", "chemical/x-cmdf")
MIMETYPE(".cml", "chemical/x-cml")
MIMETYPE(".cmp", "application/vnd.yellowriver-custom-menu")
MIMETYPE(".cmx", "image/x-cmx")
MIMETYPE(".cod", "application/vnd.rim.cod")
MIMETYPE(".coffee", "text/coffeescript")
MIMETYPE(".com", "application/x-msdownload")
MIMETYPE(".conf", "text/plain")
MIMETYPE(".cpio", "application/x-cpio")
MIMETYPE(".cpl", "application/cpl+xml")
MIMETYPE(".cpp", "text/x-c")
MIMETYPE(".cpt", "application/mac-compactpro")
MIMETYPE(".crd", "application/x-mscardfile")
MIMETYPE(".crl", "application/pkix-crl")
MIMETYPE(".crt", "application/x-x509-ca-cert")
MIMETYPE(".crx", "application/x-chrome-extension")
MIMETYPE(".cryptonote", "application/vnd.rig.cryptonote")
MIMETYPE(".csh", "application/x-csh")
MIMETYPE(".csl", "application/vnd.citationstyles.style+xml")
MIMETYPE(".csml", "chemical/x-csml")
MIMETYPE(".csp", "application/vnd.commonspace")
MIMETYPE(".css", "text/css")
MIMETYPE(".cst", "application/x-director")
MIMETYPE(".csv", "text/csv")
MIMETYPE(".cu", "application/cu-seeme")
MIMETYPE(".curl", "text/vnd.curl")
MIMETYPE(".cwl", "application/cwl")
MIMETYPE(".cww", "application/prs.cww")
MIMETYPE(".cxt", "application/x-director")
MIMETYPE(".cxx", "text/x-c")
MIMETYPE(".dae", "model/vnd.collada+xml")
MIMETYPE(".daf", "application/vnd.mobius.daf")
MIMETYPE(".dart", "application/vnd.dart")
MIMETYPE(".dataless", "application/vnd.fdsn.seed")
MIMETYPE(".davmount", "application/davmount+xml")
MIMETYPE(".dbf", "application/vnd.dbf")
MIMETYPE(".dbk", "application/docbook+xml")
MIMETYPE(".dcr", "application/x-director")
MIMETYPE(".dcurl", "text/vnd.curl.dcurl")
MIMETYPE(".dd2", "application/vnd.oma.dd2+xml")
MIMETYPE(".ddd", "application/vnd.fujixerox.ddd")
MIMETYPE(".ddf", "application/vnd.syncml.dmddf+xml")
MIMETYPE(".dds", "image/vnd.ms-dds")
MIMETYPE(".deb", "application/octet-stream")
MIMETYPE(".def", "text/plain")
MIMETYPE(".deploy", "application/octet-stream")
MIMETYPE(".der", "application/x-x509-ca-cert")
MIMETYPE(".dfac", "application/vnd.dreamfactory")
MIMETYPE(".dgc", "application/x-dgc-compressed")
MIMETYPE(".dib", "image/bmp")
MIMETYPE(".dic", "text/x-c")
MIMETYPE(".dir", "application/x-director")
MIMETYPE(".dis", "application/vnd.mobius.dis")
MIMETYPE(".disposition-notification", "message/disposition-notification")
MIMETYPE(".dist", "application/octet-stream")
MIMETYPE(".distz", "application/octet-stream")
MIMETYPE(".djv", "image/vnd.djvu")
MIMETYPE(".djvu", "image/vnd.djvu")
MIMETYPE(".dll", "application/octet-stream")
MIMETYPE(".dmg", "application/octet-stream")
MIMETYPE(".dmp", "application/vnd.tcpdump.pcap")
MIMETYPE(".dms", "application/octet-stream")
MIMETYPE(".dna", "application/vnd.dna")
MIMETYPE(".doc", "application/msword")
MIMETYPE(".docm", "application/vnd.ms-word.document.macroenabled.12")
MIMETYPE(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
MIMETYPE(".dot", "application/msword")
MIMETYPE(".dotm", "application/vnd.ms-word.template.macroenabled.12")
MIMETYPE(".dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template")
MIMETYPE(".dp", "application/vnd.osgi.dp")
MIMETYPE(".dpg", "application/vnd.dpgraph")
MIMETYPE(".dpx", "image/dpx")
MIMETYPE(".dra", "audio/vnd.dra")
MIMETYPE(".drle", "image/dicom-rle")
MIMETYPE(".dsc", "text/prs.lines.tag")
MIMETYPE(".dssc", "application/dssc+der")
MIMETYPE(".dtb", "application/x-dtbook+xml")
MIMETYPE(".dtd", "application/xml-dtd")
MIMETYPE(".dts", "audio/vnd.dts")
MIMETYPE(".dtshd", "audio/vnd.dts.hd")
MIMETYPE(".dump", "application/octet-stream")
MIMETYPE(".dvb", "video/vnd.dvb.file")
MIMETYPE(".dvi", "application/x-dvi")
MIMETYPE(".dwd", "application/atsc-dwd+xml")
MIMETYPE(".dwf", "model/vnd.dwf")
MIMETYPE(".dwg", "image/vnd.dwg")
MIMETYPE(".dxf", "image/vnd.dxf")
MIMETYPE(".dxp", "application/vnd.spotfire.dxp")
MIMETYPE(".dxr", "application/x-director")
MIMETYPE(".ear", "application/java-archive")
MIMETYPE(".ecelp4800", "audio/vnd.nuera.ecelp4800")
MIMETYPE(".ecelp7470", "audio/vnd.nuera.ecelp7470")
MIMETYPE(".ecelp9600", "audio/vnd.nuera.ecelp9600")
MIMETYPE(".ecma", "application/ecmascript")
MIMETYPE(".edm", "application/vnd.novadigm.edm")
MIMETYPE(".edx", "application/vnd.novadigm.edx")
MIMETYPE(".efif", "application/vnd.picsel")
MIMETYPE(".ei6", "application/vnd.pg.osasli")
MIMETYPE(".elc", "application/octet-stream")
MIMETYPE(".emf", "image/emf")
MIMETYPE(".eml", "message/rfc822")
MIMETYPE(".emma", "application/emma+xml")
MIMETYPE(".emotionml", "application/emotionml+xml")
MIMETYPE(".emz", "application/x-msmetafile")
MIMETYPE(".eol", "audio/vnd.digital-winds")
MIMETYPE(".eot", "application/vnd.ms-fontobject")
MIMETYPE(".eps", "application/postscript")
MIMETYPE(".epub", "application/epub+zip")
MIMETYPE(".es3", "application/vnd.eszigno3+xml")
MIMETYPE(".esa", "application/vnd.osgi.subsystem")
MIMETYPE(".esf", "application/vnd.epson.esf")
MIMETYPE(".et3", "application/vnd.eszigno3+xml")
MIMETYPE(".etx", "text/x-setext")
MIMETYPE(".eva", "application/x-eva")
MIMETYPE(".evy", "application/x-envoy")
MIMETYPE(".exe", "application/octet-stream")
MIMETYPE(".exi", "application/exi")
MIMETYPE(".exp", "application/express")
MIMETYPE(".exr", "image/aces")
MIMETYPE(".ext", "application/vnd.novadigm.ext")
MIMETYPE(".ez", "application/andrew-inset")
MIMETYPE(".ez2", "application/vnd.ezpix-album")
MIMETYPE(".ez3", "application/vnd.ezpix-package")
MIMETYPE(".f", "text/x-fortran")
MIMETYPE(".f4v", "video/x-f4v")
MIMETYPE(".f77", "text/x-fortran")
MIMETYPE(".f90", "text/x-fortran")
MIMETYPE(".fbs", "image/vnd.fastbidsheet")
MIMETYPE(".fcdt", "application/vnd.adobe.formscentral.fcdt")
MIMETYPE(".fcs", "application/vnd.isac.fcs")
MIMETYPE(".fdf", "application/fdf")
MIMETYPE(".fdt", "application/fdt+xml")
MIMETYPE(".fe_launch", "application/vnd.denovo.fcselayout-link")
MIMETYPE(".fg5", "application/vnd.fujitsu.oasysgp")
MIMETYPE(".fgd", "application/x-director")
MIMETYPE(".fh", "image/x-freehand")
MIMETYPE(".fh4", "image/x-freehand")
MIMETYPE(".fh5", "image/x-freehand")
MIMETYPE(".fh7", "image/x-freehand")
MIMETYPE(".fhc", "image/x-freehand")
MIMETYPE(".fig", "application/x-xfig")
MIMETYPE(".fits", "image/fits")
MIMETYPE(".flac", "audio/x-flac")
MIMETYPE(".fli", "video/x-fli")
MIMETYPE(".flo", "application/vnd.micrografx.flo")
MIMETYPE(".flv", "video/x-flv")
MIMETYPE(".flw", "application/vnd.kde.kivio")
MIMETYPE(".flx", "text/vnd.fmi.flexstor")
MIMETYPE(".fly", "text/vnd.fly")
MIMETYPE(".fm", "application/vnd.framemaker")
MIMETYPE(".fnc", "application/vnd.frogans.fnc")
MIMETYPE(".fo", "application/vnd.software602.filler.form+xml")
MIMETYPE(".for", "text/x-fortran")
MIMETYPE(".fpx", "image/vnd.fpx")
MIMETYPE(".frame", "application/vnd.framemaker")
MIMETYPE(".fsc", "application/vnd.fsc.weblaunch")
MIMETYPE(".fst", "image/vnd.fst")
MIMETYPE(".ftc", "application/vnd.fluxtime.clip")
MIMETYPE(".fti", "application/vnd.anser-web-funds-transfer-initiation")
MIMETYPE(".fvt", "video/vnd.fvt")
MIMETYPE(".fxp", "application/vnd.adobe.fxp")
MIMETYPE(".fxpl", "application/vnd.adobe.fxp")
MIMETYPE(".fzs", "application/vnd.fuzzysheet")
MIMETYPE(".g2w", "application/vnd.geoplan")
MIMETYPE(".g3", "image/g3fax")
MIMETYPE(".g3w", "application/vnd.geospace")
MIMETYPE(".gac", "application/vnd.groove-account")
MIMETYPE(".gam", "application/x-tads")
MIMETYPE(".gbr", "application/rpki-ghostbusters")
MIMETYPE(".gca", "application/x-gca-compressed")
MIMETYPE(".gdl", "model/vnd.gdl")
MIMETYPE(".gdoc", "application/vnd.google-apps.document")
MIMETYPE(".ged", "text/vnd.familysearch.gedcom")
MIMETYPE(".geo", "application/vnd.dynageo")
MIMETYPE(".geojson", "application/geo+json")
MIMETYPE(".gex", "application/vnd.geometry-explorer")
MIMETYPE(".ggb", "application/vnd.geogebra.file")
MIMETYPE(".ggt", "application/vnd.geogebra.tool")
MIMETYPE(".ghf", "application/vnd.groove-help")
MIMETYPE(".gif", "image/gif")
MIMETYPE(".gim", "application/vnd.groove-identity-message")
MIMETYPE(".glb", "model/gltf-binary")
MIMETYPE(".gltf", "model/gltf+json")
MIMETYPE(".gml", "application/gml+xml")
MIMETYPE(".gmx", "application/vnd.gmx")
MIMETYPE(".gnumeric", "application/x-gnumeric")
MIMETYPE(".gph", "application/vnd.flographit")
MIMETYPE(".gpx", "application/gpx+xml")
MIMETYPE(".gqf", "application/vnd.grafeq")
MIMETYPE(".gqs", "application/vnd.grafeq")
MIMETYPE(".gram", "application/srgs")
MIMETYPE(".gramps", "application/x-gramps-xml")
MIMETYPE(".gre", "application/vnd.geometry-explorer")
MIMETYPE(".grv", "application/vnd.groove-injector")
MIMETYPE(".grxml", "application/srgs+xml")
MIMETYPE(".gsf", "application/x-font-ghostscript")
MIMETYPE(".gsheet", "application/vnd.google-apps.spreadsheet")
MIMETYPE(".gslides", "application/vnd.google-apps.presentation")
MIMETYPE(".gtar", "application/x-gtar")
MIMETYPE(".gtm", "application/vnd.groove-tool-message")
MIMETYPE(".gtw", "model/vnd.gtw")
MIMETYPE(".gv", "text/vnd.graphviz")
MIMETYPE(".gxf", "application/gxf")
MIMETYPE(".gxt", "application/vnd.geonext")
MIMETYPE(".gz", "application/gzip")
MIMETYPE(".h", "text/x-c")
MIMETYPE(".h261", "video/h261")
MIMETYPE(".h263", "video/h263")
MIMETYPE(".h264", "video/h264")
MIMETYPE(".hal", "application/vnd.hal+xml")
MIMETYPE(".hbci", "application/vnd.hbci")
MIMETYPE(".hbs", "text/x-handlebars-template")
MIMETYPE(".hdd", "application/x-virtualbox-hdd")
MIMETYPE(".hdf", "application/x-hdf")
MIMETYPE(".heic", "image/heic")
MIMETYPE(".heics", "image/heic-sequence")
MIMETYPE(".heif", "image/heif")
MIMETYPE(".heifs", "image/heif-sequence")
MIMETYPE(".hej2", "image/hej2k")
MIMETYPE(".held", "application/atsc-held+xml")
MIMETYPE(".hh", "text/x-c")
MIMETYPE(".hjson", "application/hjson")
MIMETYPE(".hlp", "application/winhlp")
MIMETYPE(".hpgl", "application/vnd.hp-hpgl")
MIMETYPE(".hpid", "application/vnd.hp-hpid")
MIMETYPE(".hps", "application/vnd.hp-hps")
MIMETYPE(".hqx", "application/mac-binhex40")
MIMETYPE(".hsj2", "image/hsj2")
MIMETYPE(".htc", "text/x-component")
MIMETYPE(".htke", "application/vnd.kenameaapp")
MIMETYPE(".htm", "text/html")
MIMETYPE(".html", "text/html")
MIMETYPE(".hvd", "application/vnd.yamaha.hv-dic")
MIMETYPE(".hvp", "application/vnd.yamaha.hv-voice")
MIMETYPE(".hvs", "application/vnd.yamaha.hv-script")
MIMETYPE(".i2g", "application/vnd.intergeo")
MIMETYPE(".icc", "application/vnd.iccprofile")
MIMETYPE(".ice", "x-conference/x-cooltalk")
MIMETYPE(".icm", "application/vnd.iccprofile")
MIMETYPE(".ico", "image/vnd.microsoft.icon")
MIMETYPE(".ics", "text/calendar")
MIMETYPE(".ief", "image/ief")
MIMETYPE(".ifb", "text/calendar")
MIMETYPE(".ifm", "application/vnd.shana.informed.formdata")
MIMETYPE(".iges", "model/iges")
MIMETYPE(".igl", "application/vnd.igloader")
MIMETYPE(".igm", "application/vnd.insors.igm")
MIMETYPE(".igs", "model/iges")
MIMETYPE(".igx", "application/vnd.micrografx.igx")
MIMETYPE(".iif", "application/vnd.shana.informed.interchange")
MIMETYPE(".img", "application/octet-stream")
MIMETYPE(".imp", "application/vnd.accpac.simply.imp")
MIMETYPE(".ims", "application/vnd.ms-ims")
MIMETYPE(".in", "text/plain")
MIMETYPE(".ini", "text/plain")
MIMETYPE(".ink", "application/inkml+xml")
MIMETYPE(".inkml", "application/inkml+xml")
MIMETYPE(".install", "application/x-install-instructions")
MIMETYPE(".iota", "application/vnd.astraea-software.iota")
MIMETYPE(".ipfix", "application/ipfix")
MIMETYPE(".ipk", "application/vnd.shana.informed.package")
MIMETYPE(".irm", "application/vnd.ibm.rights-management")
MIMETYPE(".irp", "application/vnd.irepository.package+xml")
MIMETYPE(".iso", "application/octet-stream")
MIMETYPE(".itp", "application/vnd.shana.informed.formtemplate")
MIMETYPE(".its", "application/its+xml")
MIMETYPE(".ivp", "application/vnd.immervision-ivp")
MIMETYPE(".ivu", "application/vnd.immervision-ivu")
MIMETYPE(".jad", "text/vnd.sun.j2me.app-descriptor")
MIMETYPE(".jade", "text/jade")
MIMETYPE(".jam", "application/vnd.jam")
MIMETYPE(".jar", "application/java-archive")
MIMETYPE(".jardiff", "application/x-java-archive-diff")
MIMETYPE(".java", "text/x-java-source")
MIMETYPE(".jhc", "image/jphc")
MIMETYPE(".jisp", "application/vnd.jisp")
MIMETYPE(".jls", "image/jls")
MIMETYPE(".jlt", "application/vnd.hp-jlyt")
MIMETYPE(".jng", "image/x-jng")
MIMETYPE(".jnlp", "application/x-java-jnlp-file")
MIMETYPE(".joda", "application/vnd.joost.joda-archive")
MIMETYPE(".jp2", "image/jp2")
MIMETYPE(".jpe", "image/jpeg")
MIMETYPE(".jpeg", "image/jpeg")
MIMETYPE(".jpf", "image/jpx")
MIMETYPE(".jpg", "image/jpeg")
MIMETYPE(".jpg2", "image/jp2")
MIMETYPE(".jpgm", "image/jpm")
MIMETYPE(".jpgv", "video/jpeg")
MIMETYPE(".jph", "image/jph")
MIMETYPE(".jpm", "image/jpm")
MIMETYPE(".jpx", "image/jpx")
MIMETYPE(".js", "application/javascript")
MIMETYPE(".json", "application/json")
MIMETYPE(".json5", "application/json5")
MIMETYPE(".jsonld", "application/ld+json")
MIMETYPE(".jsonml", "application/jsonml+json")
MIMETYPE(".jsx", "text/jsx")
MIMETYPE(".jt", "model/jt")
MIMETYPE(".jxr", "image/jxr")
MIMETYPE(".jxra", "image/jxra")
MIMETYPE(".jxrs", "image/jxrs")
MIMETYPE(".jxs", "image/jxs")
MIMETYPE(".jxsc", "image/jxsc")
MIMETYPE(".jxsi", "image/jxsi")
MIMETYPE(".jxss", "image/jxss")
MIMETYPE(".kar", "audio/midi")
MIMETYPE(".karbon", "application/vnd.kde.karbon")
MIMETYPE(".kdbx", "application/x-keepass2")
MIMETYPE(".key", "application/vnd.apple.keynote")
MIMETYPE(".kfo", "application/vnd.kde.kformula")
MIMETYPE(".kia", "application/vnd.kidspiration")
MIMETYPE(".kml", "application/vnd.google-earth.kml+xml")
MIMETYPE(".kmz", "application/vnd.google-earth.kmz")
MIMETYPE(".kne", "application/vnd.kinar")
MIMETYPE(".knp", "application/vnd.kinar")
MIMETYPE(".kon", "application/vnd.kde.kontour")
MIMETYPE(".kpr", "application/vnd.kde.kpresenter")
MIMETYPE(".kpt", "application/vnd.kde.kpresenter")
MIMETYPE(".kpxx", "application/vnd.ds-keypoint")
MIMETYPE(".ksp", "application/vnd.kde.kspread")
MIMETYPE(".ktr", "application/vnd.kahootz")
MIMETYPE(".ktx", "image/ktx")
MIMETYPE(".ktx2", "image/ktx2")
MIMETYPE(".ktz", "application/vnd.kahootz")
MIMETYPE(".kwd", "application/vnd.kde.kword")
MIMETYPE(".kwt", "application/vnd.kde.kword")
MIMETYPE(".lasxml", "application/vnd.las.las+xml")
MIMETYPE(".latex", "application/x-latex")
MIMETYPE(".lbd", "application/vnd.llamagraphics.life-balance.desktop")
MIMETYPE(".lbe", "application/vnd.llamagraphics.life-balance.exchange+xml")
MIMETYPE(".les", "application/vnd.hhe.lesson-player")
MIMETYPE(".less", "text/less")
MIMETYPE(".lgr", "application/lgr+xml")
MIMETYPE(".lha", "application/x-lzh-compressed")
MIMETYPE(".link66", "application/vnd.route66.link66+xml")
MIMETYPE(".list", "text/plain")
MIMETYPE(".list3820", "application/vnd.ibm.modcap")
MIMETYPE(".listafp", "application/vnd.ibm.modcap")
MIMETYPE(".litcoffee", "text/coffeescript")
MIMETYPE(".lnk", "application/x-ms-shortcut")
MIMETYPE(".log", "text/plain")
MIMETYPE(".lostxml", "application/lost+xml")
MIMETYPE(".lrf", "application/octet-stream")
MIMETYPE(".lrm", "application/vnd.ms-lrm")
MIMETYPE(".ltf", "application/vnd.frogans.ltf")
MIMETYPE(".lua", "text/x-lua")
MIMETYPE(".luac", "application/x-lua-bytecode")
MIMETYPE(".lvp", "audio/vnd.lucent.voice")
MIMETYPE(".lwp", "application/vnd.lotus-wordpro")
MIMETYPE(".lzh", "application/x-lzh-compressed")
MIMETYPE(".m13", "application/x-msmediaview")
MIMETYPE(".m14", "application/x-msmediaview")
MIMETYPE(".m1v", "video/mpeg")
MIMETYPE(".m21", "application/mp21")
MIMETYPE(".m2a", "audio/mpeg")
MIMETYPE(".m2v", "video/mpeg")
MIMETYPE(".m3a", "audio/mpeg")
MIMETYPE(".m3u", "audio/x-mpegurl")
MIMETYPE(".m3u8", "application/vnd.apple.mpegurl")
MIMETYPE(".m4a", "audio/mp4")
MIMETYPE(".m4p", "application/mp4")
MIMETYPE(".m4s", "video/iso.segment")
MIMETYPE(".m4u", "video/vnd.mpegurl")
MIMETYPE(".m4v", "video/x-m4v")
MIMETYPE(".ma", "application/mathematica")
MIMETYPE(".mads", "application/mads+xml")
MIMETYPE(".maei", "application/mmt-aei+xml")
MIMETYPE(".mag", "application/vnd.ecowin.chart")
MIMETYPE(".maker", "application/vnd.framemaker")
MIMETYPE(".man", "text/troff")
MIMETYPE(".manifest", "text/cache-manifest")
MIMETYPE(".map", "application/json")
MIMETYPE(".mar", "application/octet-stream")
MIMETYPE(".markdown", "text/markdown")
MIMETYPE(".mathml", "application/mathml+xml")
MIMETYPE(".mb", "application/mathematica")
MIMETYPE(".mbk", "application/vnd.mobius.mbk")
MIMETYPE(".mbox", "application/mbox")
MIMETYPE(".mc1", "application/vnd.medcalcdata")
MIMETYPE(".mcd", "application/vnd.mcd")
MIMETYPE(".mcurl", "text/vnd.curl.mcurl")
MIMETYPE(".md", "text/markdown")
MIMETYPE(".mdb", "application/x-msaccess")
MIMETYPE(".mdi", "image/vnd.ms-modi")
MIMETYPE(".mdx", "text/mdx")
MIMETYPE(".me", "text/troff")
MIMETYPE(".mesh", "model/mesh")
MIMETYPE(".meta4", "application/metalink4+xml")
MIMETYPE(".metalink", "application/metalink+xml")
MIMETYPE(".mets", "application/mets+xml")
MIMETYPE(".mfm", "application/vnd.mfmp")
MIMETYPE(".mft", "application/rpki-manifest")
MIMETYPE(".mgp", "application/vnd.osgeo.mapguide.package")
MIMETYPE(".mgz", "application/vnd.proteus.magazine")
MIMETYPE(".mid", "audio/midi")
MIMETYPE(".midi", "audio/midi")
MIMETYPE(".mie", "application/x-mie")
MIMETYPE(".mif", "application/vnd.mif")
MIMETYPE(".mime", "message/rfc822")
MIMETYPE(".mj2", "video/mj2")
MIMETYPE(".mjp2", "video/mj2")
MIMETYPE(".mjs", "text/javascript")
MIMETYPE(".mk3d", "video/x-matroska")
MIMETYPE(".mka", "audio/x-matroska")
MIMETYPE(".mkd", "text/x-markdown")
MIMETYPE(".mks", "video/x-matroska")
MIMETYPE(".mkv", "video/x-matroska")
MIMETYPE(".mlp", "application/vnd.dolby.mlp")
MIMETYPE(".mmd", "application/vnd.chipnuts.karaoke-mmd")
MIMETYPE(".mmf", "application/vnd.smaf")
MIMETYPE(".mml", "text/mathml")
MIMETYPE(".mmr", "image/vnd.fujixerox.edmics-mmr")
MIMETYPE(".mng", "video/x-mng")
MIMETYPE(".mny", "application/x-msmoney")
MIMETYPE(".mobi", "application/x-mobipocket-ebook")
MIMETYPE(".mods", "application/mods+xml")
MIMETYPE(".mov", "video/quicktime")
MIMETYPE(".movie", "video/x-sgi-movie")
MIMETYPE(".mp2", "audio/mpeg")
MIMETYPE(".mp21", "application/mp21")
MIMETYPE(".mp2a", "audio/mpeg")
MIMETYPE(".mp3", "audio/mp3")
MIMETYPE(".mp4", "application/mp4")
MIMETYPE(".mp4a", "audio/mp4")
MIMETYPE(".mp4s", "application/mp4")
MIMETYPE(".mp4v", "video/mp4")
MIMETYPE(".mpc", "application/vnd.mophun.certificate")
MIMETYPE(".mpd", "application/dash+xml")
MIMETYPE(".mpe", "video/mpeg")
MIMETYPE(".mpeg", "video/mpeg")
MIMETYPE(".mpf", "application/media-policy-dataset+xml")
MIMETYPE(".mpg", "video/mpeg")
MIMETYPE(".mpg4", "application/mp4")
MIMETYPE(".mpga", "audio/mpeg")
MIMETYPE(".mpkg", "application/vnd.apple.installer+xml")
MIMETYPE(".mpm", "application/vnd.blueice.multipass")
MIMETYPE(".mpn", "application/vnd.mophun.application")
MIMETYPE(".mpp", "application/dash-patch+xml")
MIMETYPE(".mpt", "application/vnd.ms-project")
MIMETYPE(".mpy", "application/vnd.ibm.minipay")
MIMETYPE(".mqy", "application/vnd.mobius.mqy")
MIMETYPE(".mrc", "application/marc")
MIMETYPE(".mrcx", "application/marcxml+xml")
MIMETYPE(".ms", "text/troff")
MIMETYPE(".mscml", "application/mediaservercontrol+xml")
MIMETYPE(".mseed", "application/vnd.fdsn.mseed")
MIMETYPE(".mseq", "application/vnd.mseq")
MIMETYPE(".msf", "application/vnd.epson.msf")
MIMETYPE(".msg", "application/vnd.ms-outlook")
MIMETYPE(".msh", "model/mesh")
MIMETYPE(".msi", "application/octet-stream")
MIMETYPE(".msix", "application/msix")
MIMETYPE(".msixbundle", "application/msixbundle")
MIMETYPE(".msl", "application/vnd.mobius.msl")
MIMETYPE(".msm", "application/octet-stream")
MIMETYPE(".msp", "application/octet-stream")
MIMETYPE(".msty", "application/vnd.muvee.style")
MIMETYPE(".mtl", "model/mtl")
MIMETYPE(".mts", "model/vnd.mts")
MIMETYPE(".mus", "application/vnd.musician")
MIMETYPE(".musd", "application/mmt-usd+xml")
MIMETYPE(".musicxml", "application/vnd.recordare.musicxml+xml")
MIMETYPE(".mvb", "application/x-msmediaview")
MIMETYPE(".mvt", "application/vnd.mapbox-vector-tile")
MIMETYPE(".mwf", "application/vnd.mfer")
MIMETYPE(".mxf", "application/mxf")
MIMETYPE(".mxl", "application/vnd.recordare.musicxml")
MIMETYPE(".mxmf", "audio/mobile-xmf")
MIMETYPE(".mxml", "application/xv+xml")
MIMETYPE(".mxs", "application/vnd.triscape.mxs")
MIMETYPE(".mxu", "video/vnd.mpegurl")
MIMETYPE(".n-gage", "application/vnd.nokia.n-gage.symbian.install")
MIMETYPE(".n3", "text/n3")
MIMETYPE(".nb", "application/mathematica")
MIMETYPE(".nbp", "application/vnd.wolfram.player")
MIMETYPE(".nc", "application/x-netcdf")
MIMETYPE(".ncx", "application/x-dtbncx+xml")
MIMETYPE(".nfo", "text/x-nfo")
MIMETYPE(".ngdat", "application/vnd.nokia.n-gage.data")
MIMETYPE(".nitf", "application/vnd.nitf")
MIMETYPE(".nlu", "application/vnd.neurolanguage.nlu")
MIMETYPE(".nml", "application/vnd.enliven")
MIMETYPE(".nnd", "application/vnd.noblenet-directory")
MIMETYPE(".nns", "application/vnd.noblenet-sealer")
MIMETYPE(".nnw", "application/vnd.noblenet-web")
MIMETYPE(".npx", "image/vnd.net-fpx")
MIMETYPE(".nq", "application/n-quads")
MIMETYPE(".nsc", "application/x-conference")
MIMETYPE(".nsf", "application/vnd.lotus-notes")
MIMETYPE(".nt", "application/n-triples")
MIMETYPE(".ntf", "application/vnd.nitf")
MIMETYPE(".numbers", "application/vnd.apple.numbers")
MIMETYPE(".nzb", "application/x-nzb")
MIMETYPE(".oa2", "application/vnd.fujitsu.oasys2")
MIMETYPE(".oa3", "application/vnd.fujitsu.oasys3")
MIMETYPE(".oas", "application/vnd.fujitsu.oasys")
MIMETYPE(".obd", "application/x-msbinder")
MIMETYPE(".obgx", "application/vnd.openblox.game+xml")
MIMETYPE(".obj", "model/obj")
MIMETYPE(".oda", "application/oda")
MIMETYPE(".odb", "application/vnd.oasis.opendocument.database")
MIMETYPE(".odc", "application/vnd.oasis.opendocument.chart")
MIMETYPE(".odf", "application/vnd.oasis.opendocument.formula")
MIMETYPE(".odft", "application/vnd.oasis.opendocument.formula-template")
MIMETYPE(".odg", "application/vnd.oasis.opendocument.graphics")
MIMETYPE(".odi", "application/vnd.oasis.opendocument.image")
MIMETYPE(".odm", "application/vnd.oasis.opendocument.text-master")
MIMETYPE(".odp", "application/vnd.oasis.opendocument.presentation")
MIMETYPE(".ods", "application/vnd.oasis.opendocument.spreadsheet")
MIMETYPE(".odt", "application/vnd.oasis.opendocument.text")
MIMETYPE(".oga", "audio/ogg")
MIMETYPE(".ogex", "model/vnd.opengex")
MIMETYPE(".ogg", "audio/ogg")
MIMETYPE(".ogv", "video/ogg")
MIMETYPE(".ogx", "application/ogg")
MIMETYPE(".omdoc", "application/omdoc+xml")
MIMETYPE(".onepkg", "application/onenote")
MIMETYPE(".onetmp", "application/onenote")
MIMETYPE(".onetoc", "application/onenote")
MIMETYPE(".onetoc2", "application/onenote")
MIMETYPE(".opf", "application/oebps-package+xml")
MIMETYPE(".opml", "text/x-opml")
MIMETYPE(".oprc", "application/vnd.palm")
MIMETYPE(".opus", "audio/ogg")
MIMETYPE(".org", "application/vnd.lotus-organizer")
MIMETYPE(".osf", "application/vnd.yamaha.openscoreformat")
MIMETYPE(".osfpvg", "application/vnd.yamaha.openscoreformat.osfpvg+xml")
MIMETYPE(".osm", "application/vnd.openstreetmap.data+xml")
MIMETYPE(".otc", "application/vnd.oasis.opendocument.chart-template")
MIMETYPE(".otf", "font/otf")
MIMETYPE(".otg", "application/vnd.oasis.opendocument.graphics-template")
MIMETYPE(".oth", "application/vnd.oasis.opendocument.text-web")
MIMETYPE(".oti", "application/vnd.oasis.opendocument.image-template")
MIMETYPE(".otp", "application/vnd.oasis.opendocument.presentation-template")
MIMETYPE(".ots", "application/vnd.oasis.opendocument.spreadsheet-template")
MIMETYPE(".ott", "application/vnd.oasis.opendocument.text-template")
MIMETYPE(".ova", "application/x-virtualbox-ova")
MIMETYPE(".ovf", "application/x-virtualbox-ovf")
MIMETYPE(".owl", "application/rdf+xml")
MIMETYPE(".oxps", "application/oxps")
MIMETYPE(".oxt", "application/vnd.openofficeorg.extension")
MIMETYPE(".p", "text/x-pascal")
MIMETYPE(".p10", "application/pkcs10")
MIMETYPE(".p12", "application/x-pkcs12")
MIMETYPE(".p7b", "application/x-pkcs7-certificates")
MIMETYPE(".p7c", "application/pkcs7-mime")
MIMETYPE(".p7m", "application/pkcs7-mime")
MIMETYPE(".p7r", "application/x-pkcs7-certreqresp")
MIMETYPE(".p7s", "application/pkcs7-signature")
MIMETYPE(".p8", "application/pkcs8")
MIMETYPE(".pac", "application/x-ns-proxy-autoconfig")
MIMETYPE(".pages", "application/vnd.apple.pages")
MIMETYPE(".pas", "text/x-pascal")
MIMETYPE(".paw", "application/vnd.pawaafile")
MIMETYPE(".pbd", "application/vnd.powerbuilder6")
MIMETYPE(".pbm", "image/x-portable-bitmap")
MIMETYPE(".pcap", "application/vnd.tcpdump.pcap")
MIMETYPE(".pcf", "application/x-font-pcf")
MIMETYPE(".pcl", "application/vnd.hp-pcl")
MIMETYPE(".pclxl", "application/vnd.hp-pclxl")
MIMETYPE(".pct", "image/x-pict")
MIMETYPE(".pcurl", "application/vnd.curl.pcurl")
MIMETYPE(".pcx", "image/vnd.zbrush.pcx")
MIMETYPE(".pdb", "application/vnd.palm")
MIMETYPE(".pde", "text/x-processing")
MIMETYPE(".pdf", "application/pdf")
MIMETYPE(".pem", "application/x-x509-ca-cert")
MIMETYPE(".pfa", "application/x-font-type1")
MIMETYPE(".pfb", "application/x-font-type1")
MIMETYPE(".pfm", "application/x-font-type1")
MIMETYPE(".pfr", "application/font-tdpfr")
MIMETYPE(".pfx", "application/x-pkcs12")
MIMETYPE(".pgm", "image/x-portable-graymap")
MIMETYPE(".pgn", "application/x-chess-pgn")
MIMETYPE(".pgp", "application/pgp-encrypted")
MIMETYPE(".php", "application/x-httpd-php")
MIMETYPE(".pic", "image/x-pict")
MIMETYPE(".pkg", "application/octet-stream")
MIMETYPE(".pki", "application/pkixcmp")
MIMETYPE(".pkipath", "application/pkix-pkipath")
MIMETYPE(".pkpass", "application/vnd.apple.pkpass")
MIMETYPE(".pl", "application/x-perl")
MIMETYPE(".plb", "application/vnd.3gpp.pic-bw-large")
MIMETYPE(".plc", "application/vnd.mobius.plc")
MIMETYPE(".plf", "application/vnd.pocketlearn")
MIMETYPE(".pls", "application/pls+xml")
MIMETYPE(".pm", "application/x-perl")
MIMETYPE(".pml", "application/vnd.ctc-posml")
MIMETYPE(".png", "image/png")
MIMETYPE(".pnm", "image/x-portable-anymap")
MIMETYPE(".portpkg", "application/vnd.macports.portpkg")
MIMETYPE(".pot", "application/vnd.ms-powerpoint")
MIMETYPE(".potm", "application/vnd.ms-powerpoint.template.macroenabled.12")
MIMETYPE(".potx", "application/vnd.openxmlformats-officedocument.presentationml.template")
MIMETYPE(".ppam", "application/vnd.ms-powerpoint.addin.macroenabled.12")
MIMETYPE(".ppd", "application/vnd.cups-ppd")
MIMETYPE(".ppm", "image/x-portable-pixmap")
MIMETYPE(".pps", "application/vnd.ms-powerpoint")
MIMETYPE(".ppsm", "application/vnd.ms-powerpoint.slideshow.macroenabled.12")
MIMETYPE(".ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow")
MIMETYPE(".ppt", "application/vnd.ms-powerpoint")
MIMETYPE(".pptm", "application/vnd.ms-powerpoint.presentation.macroenabled.12")
MIMETYPE(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation")
MIMETYPE(".pqa", "application/vnd.palm")
MIMETYPE(".prc", "model/prc")
MIMETYPE(".pre", "application/vnd.lotus-freelance")
MIMETYPE(".prf", "application/pics-rules")
MIMETYPE(".provx", "application/provenance+xml")
MIMETYPE(".ps", "application/postscript")
MIMETYPE(".psb", "application/vnd.3gpp.pic-bw-small")
MIMETYPE(".psd", "image/vnd.adobe.photoshop")
MIMETYPE(".psf", "application/x-font-linux-psf")
MIMETYPE(".pskcxml", "application/pskc+xml")
MIMETYPE(".pti", "image/prs.pti")
MIMETYPE(".ptid", "application/vnd.pvi.ptid1")
MIMETYPE(".pub", "application/x-mspublisher")
MIMETYPE(".pvb", "application/vnd.3gpp.pic-bw-var")
MIMETYPE(".pwn", "application/vnd.3m.post-it-notes")
MIMETYPE(".pya", "audio/vnd.ms-playready.media.pya")
MIMETYPE(".pyo", "model/vnd.pytha.pyox")
MIMETYPE(".pyox", "model/vnd.pytha.pyox")
MIMETYPE(".pyv", "video/vnd.ms-playready.media.pyv")
MIMETYPE(".qam", "application/vnd.epson.quickanime")
MIMETYPE(".qbo", "application/vnd.intu.qbo")
MIMETYPE(".qfx", "application/vnd.intu.qfx")
MIMETYPE(".qps", "application/vnd.publishare-delta-tree")
MIMETYPE(".qt", "video/quicktime")
MIMETYPE(".qwd", "application/vnd.quark.quarkxpress")
MIMETYPE(".qwt", "application/vnd.quark.quarkxpress")
MIMETYPE(".qxb", "application/vnd.quark.quarkxpress")
MIMETYPE(".qxd", "application/vnd.quark.quarkxpress")
MIMETYPE(".qxl", "application/vnd.quark.quarkxpress")
MIMETYPE(".qxt", "application/vnd.quark.quarkxpress")
MIMETYPE(".ra", "audio/x-pn-realaudio")
MIMETYPE(".ram", "audio/x-pn-realaudio")
MIMETYPE(".raml", "application/raml+yaml")
MIMETYPE(".rapd", "application/route-apd+xml")
MIMETYPE(".rar", "application/vnd.rar")
MIMETYPE(".ras", "image/x-cmu-raster")
MIMETYPE(".rcprofile", "application/vnd.ipunplugged.rcprofile")
MIMETYPE(".rdf", "application/rdf+xml")
MIMETYPE(".rdz", "application/vnd.data-vision.rdz")
MIMETYPE(".relo", "application/p2p-overlay+xml")
MIMETYPE(".rep", "application/vnd.businessobjects")
MIMETYPE(".res", "application/x-dtbresource+xml")
MIMETYPE(".rgb", "image/x-rgb")
MIMETYPE(".rif", "application/reginfo+xml")
MIMETYPE(".rip", "audio/vnd.rip")
MIMETYPE(".ris", "application/x-research-info-systems")
MIMETYPE(".rl", "application/resource-lists+xml")
MIMETYPE(".rlc", "image/vnd.fujixerox.edmics-rlc")
MIMETYPE(".rld", "application/resource-lists-diff+xml")
MIMETYPE(".rm", "application/vnd.rn-realmedia")
MIMETYPE(".rmi", "audio/midi")
MIMETYPE(".rmp", "audio/x-pn-realaudio-plugin")
MIMETYPE(".rms", "application/vnd.jcp.javame.midlet-rms")
MIMETYPE(".rmvb", "application/vnd.rn-realmedia-vbr")
MIMETYPE(".rnc", "application/relax-ng-compact-syntax")
MIMETYPE(".rng", "application/xml")
MIMETYPE(".roa", "application/rpki-roa")
MIMETYPE(".roff", "text/troff")
MIMETYPE(".rp9", "application/vnd.cloanto.rp9")
MIMETYPE(".rpm", "application/x-redhat-package-manager")
MIMETYPE(".rpss", "application/vnd.nokia.radio-presets")
MIMETYPE(".rpst", "application/vnd.nokia.radio-preset")
MIMETYPE(".rq", "application/sparql-query")
MIMETYPE(".rs", "application/rls-services+xml")
MIMETYPE(".rsat", "application/atsc-rsat+xml")
MIMETYPE(".rsd", "application/rsd+xml")
MIMETYPE(".rsheet", "application/urc-ressheet+xml")
MIMETYPE(".rss", "application/rss+xml")
MIMETYPE(".rtf", "application/rtf")
MIMETYPE(".rtx", "text/richtext")
MIMETYPE(".run", "application/x-makeself")
MIMETYPE(".rusd", "application/route-usd+xml")
MIMETYPE(".s", "text/x-asm")
MIMETYPE(".s3m", "audio/s3m")
MIMETYPE(".saf", "application/vnd.yamaha.smaf-audio")
MIMETYPE(".sass", "text/x-sass")
MIMETYPE(".sbml", "application/sbml+xml")
MIMETYPE(".sc", "application/vnd.ibm.secure-container")
MIMETYPE(".scd", "application/x-msschedule")
MIMETYPE(".scm", "application/vnd.lotus-screencam")
MIMETYPE(".scq", "application/scvp-cv-request")
MIMETYPE(".scs", "application/scvp-cv-response")
MIMETYPE(".scss", "text/x-scss")
MIMETYPE(".scurl", "text/vnd.curl.scurl")
MIMETYPE(".sda", "application/vnd.stardivision.draw")
MIMETYPE(".sdc", "application/vnd.stardivision.calc")
MIMETYPE(".sdd", "application/vnd.stardivision.impress")
MIMETYPE(".sdkd", "application/vnd.solent.sdkm+xml")
MIMETYPE(".sdkm", "application/vnd.solent.sdkm+xml")
MIMETYPE(".sdp", "application/sdp")
MIMETYPE(".sdw", "application/vnd.stardivision.writer")
MIMETYPE(".sea", "application/x-sea")
MIMETYPE(".see", "application/vnd.seemail")
MIMETYPE(".seed", "application/vnd.fdsn.seed")
MIMETYPE(".sema", "application/vnd.sema")
MIMETYPE(".semd", "application/vnd.semd")
MIMETYPE(".semf", "application/vnd.semf")
MIMETYPE(".senmlx", "application/senml+xml")
MIMETYPE(".sensmlx", "application/sensml+xml")
MIMETYPE(".ser", "application/java-serialized-object")
MIMETYPE(".setpay", "application/set-payment-initiation")
MIMETYPE(".setreg", "application/set-registration-initiation")
MIMETYPE(".sfd-hdstx", "application/vnd.hydrostatix.sof-data")
MIMETYPE(".sfs", "application/vnd.spotfire.sfs")
MIMETYPE(".sfv", "text/x-sfv")
MIMETYPE(".sgi", "image/sgi")
MIMETYPE(".sgl", "application/vnd.stardivision.writer-global")
MIMETYPE(".sgm", "text/sgml")
MIMETYPE(".sgml", "text/sgml")
MIMETYPE(".sh", "application/x-sh")
MIMETYPE(".shar", "application/x-shar")
MIMETYPE(".shex", "text/shex")
MIMETYPE(".shf", "application/shf+xml")
MIMETYPE(".shtml", "text/html")
MIMETYPE(".sid", "image/x-mrsid-image")
MIMETYPE(".sieve", "application/sieve")
MIMETYPE(".sig", "application/pgp-signature")
MIMETYPE(".sil", "audio/silk")
MIMETYPE(".silo", "model/mesh")
MIMETYPE(".sis", "application/vnd.symbian.install")
MIMETYPE(".sisx", "application/vnd.symbian.install")
MIMETYPE(".sit", "application/x-stuffit")
MIMETYPE(".sitx", "application/x-stuffitx")
MIMETYPE(".siv", "application/sieve")
MIMETYPE(".skd", "application/vnd.koan")
MIMETYPE(".skm", "application/vnd.koan")
MIMETYPE(".skp", "application/vnd.koan")
MIMETYPE(".skt", "application/vnd.koan")
MIMETYPE(".sldm", "application/vnd.ms-powerpoint.slide.macroenabled.12")
MIMETYPE(".sldx", "application/vnd.openxmlformats-officedocument.presentationml.slide")
MIMETYPE(".slim", "text/slim")
MIMETYPE(".slm", "text/slim")
MIMETYPE(".sls", "application/route-s-tsid+xml")
MIMETYPE(".slt", "application/vnd.epson.salt")
MIMETYPE(".sm", "application/vnd.stepmania.stepchart")
MIMETYPE(".smf", "application/vnd.stardivision.math")
MIMETYPE(".smi", "application/smil+xml")
MIMETYPE(".smil", "application/smil+xml")
MIMETYPE(".smv", "video/x-smv")
MIMETYPE(".smzip", "application/vnd.stepmania.package")
MIMETYPE(".snd", "audio/basic")
MIMETYPE(".snf", "application/x-font-snf")
MIMETYPE(".so", "application/octet-stream")
MIMETYPE(".spc", "application/x-pkcs7-certificates")
MIMETYPE(".spdx", "text/spdx")
MIMETYPE(".spf", "application/vnd.yamaha.smaf-phrase")
MIMETYPE(".spl", "application/x-futuresplash")
MIMETYPE(".spot", "text/vnd.in3d.spot")
MIMETYPE(".spp", "application/scvp-vp-response")
MIMETYPE(".spq", "application/scvp-vp-request")
MIMETYPE(".spx", "audio/ogg")
MIMETYPE(".sql", "application/sql")
MIMETYPE(".src", "application/x-wais-source")
MIMETYPE(".srt", "application/x-subrip")
MIMETYPE(".sru", "application/sru+xml")
MIMETYPE(".srx", "application/sparql-results+xml")
MIMETYPE(".ssdl", "application/ssdl+xml")
MIMETYPE(".sse", "application/vnd.kodak-descriptor")
MIMETYPE(".ssf", "application/vnd.epson.ssf")
MIMETYPE(".ssml", "application/ssml+xml")
MIMETYPE(".st", "application/vnd.sailingtracker.track")
MIMETYPE(".stc", "application/vnd.sun.xml.calc.template")
MIMETYPE(".std", "application/vnd.sun.xml.draw.template")
MIMETYPE(".stf", "application/vnd.wt.stf")
MIMETYPE(".sti", "application/vnd.sun.xml.impress.template")
MIMETYPE(".stk", "application/hyperstudio")
MIMETYPE(".stl", "application/vnd.ms-pki.stl")
MIMETYPE(".stpx", "model/step+xml")
MIMETYPE(".stpxz", "model/step-xml+zip")
MIMETYPE(".stpz", "model/step+zip")
MIMETYPE(".str", "application/vnd.pg.format")
MIMETYPE(".stw", "application/vnd.sun.xml.writer.template")
MIMETYPE(".styl", "text/stylus")
MIMETYPE(".stylus", "text/stylus")
MIMETYPE(".sub", "image/vnd.dvb.subtitle")
MIMETYPE(".sus", "application/vnd.sus-calendar")
MIMETYPE(".susp", "application/vnd.sus-calendar")
MIMETYPE(".sv4cpio", "application/x-sv4cpio")
MIMETYPE(".sv4crc", "application/x-sv4crc")
MIMETYPE(".svc", "application/vnd.dvb.service")
MIMETYPE(".svd", "application/vnd.svd")
MIMETYPE(".svg", "image/svg+xml")
MIMETYPE(".svgz", "image/svg+xml")
MIMETYPE(".swa", "application/x-director")
MIMETYPE(".swf", "application/x-shockwave-flash")
MIMETYPE(".swi", "application/vnd.aristanetworks.swi")
MIMETYPE(".swidtag", "application/swid+xml")
MIMETYPE(".sxc", "application/vnd.sun.xml.calc")
MIMETYPE(".sxd", "application/vnd.sun.xml.draw")
MIMETYPE(".sxg", "application/vnd.sun.xml.writer.global")
MIMETYPE(".sxi", "application/vnd.sun.xml.impress")
MIMETYPE(".sxm", "application/vnd.sun.xml.math")
MIMETYPE(".sxw", "application/vnd.sun.xml.writer")
MIMETYPE(".t", "text/troff")
MIMETYPE(".t3", "application/x-t3vm-image")
MIMETYPE(".t38", "image/t38")
MIMETYPE(".taglet", "application/vnd.mynfc")
MIMETYPE(".tao", "application/vnd.tao.intent-module-archive")
MIMETYPE(".tap", "image/vnd.tencent.tap")
MIMETYPE(".tar", "application/x-tar")
MIMETYPE(".tcap", "application/vnd.3gpp2.tcap")
MIMETYPE(".tcl", "application/x-tcl")
MIMETYPE(".td", "application/urc-targetdesc+xml")
MIMETYPE(".teacher", "application/vnd.smart.teacher")
MIMETYPE(".tei", "application/tei+xml")
MIMETYPE(".teicorpus", "application/tei+xml")
MIMETYPE(".tex", "application/x-tex")
MIMETYPE(".texi", "application/x-texinfo")
MIMETYPE(".texinfo", "application/x-texinfo")
MIMETYPE(".text", "text/plain")
MIMETYPE(".tfi", "application/thraud+xml")
MIMETYPE(".tfm", "application/x-tex-tfm")
MIMETYPE(".tfx", "image/tiff-fx")
MIMETYPE(".tga", "image/x-tga")
MIMETYPE(".thmx", "application/vnd.ms-officetheme")
MIMETYPE(".tif", "image/tiff")
MIMETYPE(".tiff", "image/tiff")
MIMETYPE(".tk", "application/x-tcl")
MIMETYPE(".tmo", "application/vnd.tmobile-livetv")
MIMETYPE(".toml", "application/toml")
MIMETYPE(".torrent", "application/x-bittorrent")
MIMETYPE(".tpl", "application/vnd.groove-tool-template")
MIMETYPE(".tpt", "application/vnd.trid.tpt")
MIMETYPE(".tr", "text/troff")
MIMETYPE(".tra", "application/vnd.trueapp")
MIMETYPE(".trig", "application/trig")
MIMETYPE(".trm", "application/x-msterminal")
MIMETYPE(".ts", "video/mp2t")
MIMETYPE(".tsd", "application/timestamped-data")
MIMETYPE(".tsv", "text/tab-separated-values")
MIMETYPE(".ttc", "font/collection")
MIMETYPE(".ttf", "font/ttf")
MIMETYPE(".ttl", "text/turtle")
MIMETYPE(".ttml", "application/ttml+xml")
MIMETYPE(".twd", "application/vnd.simtech-mindmapper")
MIMETYPE(".twds", "application/vnd.simtech-mindmapper")
MIMETYPE(".txd", "application/vnd.genomatix.tuxedo")
MIMETYPE(".txf", "application/vnd.mobius.txf")
MIMETYPE(".txt", "text/plain")
MIMETYPE(".u32", "application/x-authorware-bin")
MIMETYPE(".u3d", "model/u3d")
MIMETYPE(".u8dsn", "message/global-delivery-status")
MIMETYPE(".u8hdr", "message/global-headers")
MIMETYPE(".u8mdn", "message/global-disposition-notification")
MIMETYPE(".u8msg", "message/global")
MIMETYPE(".ubj", "application/ubjson")
MIMETYPE(".udeb", "application/x-debian-package")
MIMETYPE(".ufd", "application/vnd.ufdl")
MIMETYPE(".ufdl", "application/vnd.ufdl")
MIMETYPE(".ulx", "application/x-glulx")
MIMETYPE(".umj", "application/vnd.umajin")
MIMETYPE(".unityweb", "application/vnd.unity")
MIMETYPE(".uo", "application/vnd.uoml+xml")
MIMETYPE(".uoml", "application/vnd.uoml+xml")
MIMETYPE(".uri", "text/uri-list")
MIMETYPE(".uris", "text/uri-list")
MIMETYPE(".urls", "text/uri-list")
MIMETYPE(".usda", "model/vnd.usda")
MIMETYPE(".usdz", "model/vnd.usdz+zip")
MIMETYPE(".ustar", "application/x-ustar")
MIMETYPE(".utz", "application/vnd.uiq.theme")
MIMETYPE(".uu", "text/x-uuencode")
MIMETYPE(".uva", "audio/vnd.dece.audio")
MIMETYPE(".uvd", "application/vnd.dece.data")
MIMETYPE(".uvf", "application/vnd.dece.data")
MIMETYPE(".uvg", "image/vnd.dece.graphic")
MIMETYPE(".uvh", "video/vnd.dece.hd")
MIMETYPE(".uvi", "image/vnd.dece.graphic")
MIMETYPE(".uvm", "video/vnd.dece.mobile")
MIMETYPE(".uvp", "video/vnd.dece.pd")
MIMETYPE(".uvs", "video/vnd.dece.sd")
MIMETYPE(".uvt", "application/vnd.dece.ttml+xml")
MIMETYPE(".uvu", "video/vnd.uvvu.mp4")
MIMETYPE(".uvv", "video/vnd.dece.video")
MIMETYPE(".uvva", "audio/vnd.dece.audio")
MIMETYPE(".uvvd", "application/vnd.dece.data")
MIMETYPE(".uvvf", "application/vnd.dece.data")
MIMETYPE(".uvvg", "image/vnd.dece.graphic")
MIMETYPE(".uvvh", "video/vnd.dece.hd")
MIMETYPE(".uvvi", "image/vnd.dece.graphic")
MIMETYPE(".uvvm", "video/vnd.dece.mobile")
MIMETYPE(".uvvp", "video/vnd.dece.pd")
MIMETYPE(".uvvs", "video/vnd.dece.sd")
MIMETYPE(".uvvt", "application/vnd.dece.ttml+xml")
MIMETYPE(".uvvu", "video/vnd.uvvu.mp4")
MIMETYPE(".uvvv", "video/vnd.dece.video")
MIMETYPE(".uvvx", "application/vnd.dece.unspecified")
MIMETYPE(".uvvz", "application/vnd.dece.zip")
MIMETYPE(".uvx", "application/vnd.dece.unspecified")
MIMETYPE(".uvz", "application/vnd.dece.zip")
MIMETYPE(".vbox", "application/x-virtualbox-vbox")
MIMETYPE(".vbox-extpack", "application/x-virtualbox-vbox-extpack")
MIMETYPE(".vcard", "text/vcard")
MIMETYPE(".vcd", "application/x-cdlink")
MIMETYPE(".vcf", "text/x-vcard")
MIMETYPE(".vcg", "application/vnd.groove-vcard")
MIMETYPE(".vcs", "text/x-vcalendar")
MIMETYPE(".vcx", "application/vnd.vcx")
MIMETYPE(".vdi", "application/x-virtualbox-vdi")
MIMETYPE(".vds", "model/vnd.sap.vds")
MIMETYPE(".vhd", "application/x-virtualbox-vhd")
MIMETYPE(".vis", "application/vnd.visionary")
MIMETYPE(".viv", "video/vnd.vivo")
MIMETYPE(".vmdk", "application/x-virtualbox-vmdk")
MIMETYPE(".vob", "video/x-ms-vob")
MIMETYPE(".vor", "application/vnd.stardivision.writer")
MIMETYPE(".vox", "application/x-authorware-bin")
MIMETYPE(".vrml", "model/vrml")
MIMETYPE(".vsd", "application/vnd.visio")
MIMETYPE(".vsf", "application/vnd.vsf")
MIMETYPE(".vss", "application/vnd.visio")
MIMETYPE(".vst", "application/vnd.visio")
MIMETYPE(".vsw", "application/vnd.visio")
MIMETYPE(".vtf", "image/vnd.valve.source.texture")
MIMETYPE(".vtt", "text/vtt")
MIMETYPE(".vtu", "model/vnd.vtu")
MIMETYPE(".vxml", "application/voicexml+xml")
MIMETYPE(".w3d", "application/x-director")
MIMETYPE(".wad", "application/x-doom")
MIMETYPE(".wadl", "application/vnd.sun.wadl+xml")
MIMETYPE(".war", "application/java-archive")
MIMETYPE(".wasm", "application/wasm")
MIMETYPE(".wav", "audio/wav")
MIMETYPE(".wax", "audio/x-ms-wax")
MIMETYPE(".wbmp", "image/vnd.wap.wbmp")
MIMETYPE(".wbs", "application/vnd.criticaltools.wbs+xml")
MIMETYPE(".wbxml", "application/vnd.wap.wbxml")
MIMETYPE(".wcm", "application/vnd.ms-works")
MIMETYPE(".wdb", "application/vnd.ms-works")
MIMETYPE(".wdp", "image/vnd.ms-photo")
MIMETYPE(".weba", "audio/webm")
MIMETYPE(".webapp", "application/x-web-app-manifest+json")
MIMETYPE(".webm", "video/webm")
MIMETYPE(".webmanifest", "application/manifest+json")
MIMETYPE(".webp", "image/webp")
MIMETYPE(".wg", "application/vnd.pmi.widget")
MIMETYPE(".wgsl", "text/wgsl")
MIMETYPE(".wgt", "application/widget")
MIMETYPE(".wif", "application/watcherinfo+xml")
MIMETYPE(".wks", "application/vnd.ms-works")
MIMETYPE(".wm", "video/x-ms-wm")
MIMETYPE(".wma", "audio/x-ms-wma")
MIMETYPE(".wmd", "application/x-ms-wmd")
MIMETYPE(".wmf", "image/wmf")
MIMETYPE(".wml", "text/vnd.wap.wml")
MIMETYPE(".wmlc", "application/vnd.wap.wmlc")
MIMETYPE(".wmls", "text/vnd.wap.wmlscript")
MIMETYPE(".wmlsc", "application/vnd.wap.wmlscriptc")
MIMETYPE(".wmv", "video/x-ms-wmv")
MIMETYPE(".wmx", "video/x-ms-wmx")
MIMETYPE(".wmz", "application/x-msmetafile")
MIMETYPE(".woff", "font/woff")
MIMETYPE(".woff2", "font/woff2")
MIMETYPE(".wpd", "application/vnd.wordperfect")
MIMETYPE(".wpl", "application/vnd.ms-wpl")
MIMETYPE(".wps", "application/vnd.ms-works")
MIMETYPE(".wqd", "application/vnd.wqd")
MIMETYPE(".wri", "application/x-mswrite")
MIMETYPE(".wrl", "model/vrml")
MIMETYPE(".wsc", "message/vnd.wfa.wsc")
MIMETYPE(".wsdl", "application/wsdl+xml")
MIMETYPE(".wspolicy", "application/wspolicy+xml")
MIMETYPE(".wtb", "application/vnd.webturbo")
MIMETYPE(".wvx", "video/x-ms-wvx")
MIMETYPE(".x32", "application/x-authorware-bin")
MIMETYPE(".x3d", "model/x3d+xml")
MIMETYPE(".x3db", "model/x3d+binary")
MIMETYPE(".x3dbz", "model/x3d+binary")
MIMETYPE(".x3dv", "model/x3d+vrml")
MIMETYPE(".x3dvz", "model/x3d+vrml")
MIMETYPE(".x3dz", "model/x3d+xml")
MIMETYPE(".x_b", "model/vnd.parasolid.transmit.binary")
MIMETYPE(".x_t", "model/vnd.parasolid.transmit.text")
MIMETYPE(".xaml", "application/xaml+xml")
MIMETYPE(".xap", "application/x-silverlight-app")
MIMETYPE(".xar", "application/vnd.xara")
MIMETYPE(".xav", "application/xcap-att+xml")
MIMETYPE(".xbap", "application/x-ms-xbap")
MIMETYPE(".xbd", "application/vnd.fujixerox.docuworks.binder")
MIMETYPE(".xbm", "image/x-xbitmap")
MIMETYPE(".xca", "application/xcap-caps+xml")
MIMETYPE(".xcs", "application/calendar+xml")
MIMETYPE(".xdf", "application/xcap-diff+xml")
MIMETYPE(".xdm", "application/vnd.syncml.dm+xml")
MIMETYPE(".xdp", "application/vnd.adobe.xdp+xml")
MIMETYPE(".xdssc", "application/dssc+xml")
MIMETYPE(".xdw", "application/vnd.fujixerox.docuworks")
MIMETYPE(".xel", "application/xcap-el+xml")
MIMETYPE(".xenc", "application/xenc+xml")
MIMETYPE(".xer", "application/patch-ops-error+xml")
MIMETYPE(".xfdf", "application/vnd.adobe.xfdf")
MIMETYPE(".xfdl", "application/vnd.xfdl")
MIMETYPE(".xht", "application/xhtml+xml")
MIMETYPE(".xhtm", "application/vnd.pwg-xhtml-print+xml")
MIMETYPE(".xhtml", "application/xhtml+xml")
MIMETYPE(".xhvml", "application/xv+xml")
MIMETYPE(".xif", "image/vnd.xiff")
MIMETYPE(".xla", "application/vnd.ms-excel")
MIMETYPE(".xlam", "application/vnd.ms-excel.addin.macroenabled.12")
MIMETYPE(".xlc", "application/vnd.ms-excel")
MIMETYPE(".xlf", "application/xliff+xml")
MIMETYPE(".xlm", "application/vnd.ms-excel")
MIMETYPE(".xls", "application/vnd.ms-excel")
MIMETYPE(".xlsb", "application/vnd.ms-excel.sheet.binary.macroenabled.12")
MIMETYPE(".xlsm", "application/vnd.ms-excel.sheet.macroenabled.12")
MIMETYPE(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
MIMETYPE(".xlt", "application/vnd.ms-excel")
MIMETYPE(".xltm", "application/vnd.ms-excel.template.macroenabled.12")
MIMETYPE(".xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template")
MIMETYPE(".xlw", "application/vnd.ms-excel")
MIMETYPE(".xm", "audio/xm")
MIMETYPE(".xml", "application/xml")
MIMETYPE(".xns", "application/xcap-ns+xml")
MIMETYPE(".xo", "application/vnd.olpc-sugar")
MIMETYPE(".xop", "application/xop+xml")
MIMETYPE(".xpi", "application/x-xpinstall")
MIMETYPE(".xpl", "application/xproc+xml")
MIMETYPE(".xpm", "image/x-xpixmap")
MIMETYPE(".xpr", "application/vnd.is-xpr")
MIMETYPE(".xps", "application/vnd.ms-xpsdocument")
MIMETYPE(".xpw", "application/vnd.intercon.formnet")
MIMETYPE(".xpx", "application/vnd.intercon.formnet")
MIMETYPE(".xsd", "application/xml")
MIMETYPE(".xsf", "application/prs.xsf+xml")
MIMETYPE(".xsl", "application/xml")
MIMETYPE(".xslt", "application/xslt+xml")
MIMETYPE(".xsm", "application/vnd.syncml+xml")
MIMETYPE(".xspf", "application/xspf+xml")
MIMETYPE(".xul", "application/vnd.mozilla.xul+xml")
MIMETYPE(".xvm", "application/xv+xml")
MIMETYPE(".xvml", "application/xv+xml")
MIMETYPE(".xwd", "image/x-xwindowdump")
MIMETYPE(".xyz", "chemical/x-xyz")
MIMETYPE(".xz", "application/x-xz")
MIMETYPE(".yaml", "text/yaml")
MIMETYPE(".yang", "application/yang")
MIMETYPE(".yin", "application/yin+xml")
MIMETYPE(".yml", "text/yaml")
MIMETYPE(".ymp", "text/x-suse-ymp")
MIMETYPE(".z1", "application/x-zmachine")
MIMETYPE(".z2", "application/x-zmachine")
MIMETYPE(".z3", "application/x-zmachine")
MIMETYPE(".z4", "application/x-zmachine")
MIMETYPE(".z5", "application/x-zmachine")
MIMETYPE(".z6", "application/x-zmachine")
MIMETYPE(".z7", "application/x-zmachine")
MIMETYPE(".z8", "application/x-zmachine")
MIMETYPE(".zaz", "application/vnd.zzazz.deck+xml")
MIMETYPE(".zip", "application/zip")
MIMETYPE(".zir", "application/vnd.zul")
MIMETYPE(".zirz", "application/vnd.zul")
MIMETYPE(".zmm", "application/vnd.handheld-entertainment+xml")

#undef MIMETYPE
