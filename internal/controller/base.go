package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/langgenius/dify-sandbox/internal/types"
)

func BindRequest[T any](r *gin.Context, success func(T)) {
	var request T
	var err error

	context_type := r.<PERSON>("Content-Type")
	if context_type == "application/json" {
		err = r.BindJ<PERSON>(&request)
	} else {
		err = r.ShouldBind(&request)
	}

	if err != nil {
		resp := types.ErrorResponse(-400, err.Error())
		r.<PERSON><PERSON>(200, resp)
		return
	}
	success(request)
}
