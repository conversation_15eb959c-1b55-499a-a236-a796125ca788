FROM golang:1.23.9 AS builder

COPY . /app
WORKDIR /app

# if you located in China, you can use aliyun mirror to speed up
# && echo "deb http://mirrors.aliyun.com/debian testing main" > /etc/apt/sources.list

# install dependencies and build binary
RUN apt-get update && apt-get install -y pkg-config gcc libseccomp-dev && go mod tidy && bash ./build/build_arm64.sh

FROM python:3.10-slim-bookworm as tester

# if you located in China, you can use aliyun mirror to speed up
# && echo "deb http://mirrors.aliyun.com/debian testing main" > /etc/apt/sources.list

# install system dependencies
RUN echo "deb http://deb.debian.org/debian testing main" > /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
       pkg-config \
       libseccomp-dev \
       wget \
       curl \
       xz-utils \
       zlib1g \
       expat \
       perl \
       libsqlite3-0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# workdir
WORKDIR /app

# checkout
COPY . /app

# copy binary and env from builder
COPY --from=builder /app/internal/core/runner/python/python.so /app/internal/core/runner/python/python.so
COPY --from=builder /app/internal/core/runner/nodejs/nodejs.so /app/internal/core/runner/nodejs/nodejs.so

# copy test config file
COPY conf/config.yaml /conf/config.yaml
# copy python dependencies
COPY dependencies/python-requirements.txt /dependencies/python-requirements.txt

# install python dependencies
RUN pip3 install --no-cache-dir httpx==0.27.2 requests==2.32.3 jinja2==3.1.6 PySocks httpx[socks]

# install node
RUN wget -O /opt/node-v20.11.1-linux-arm64.tar.xz https://npmmirror.com/mirrors/node/v20.11.1/node-v20.11.1-linux-arm64.tar.xz \
    && tar -xvf /opt/node-v20.11.1-linux-arm64.tar.xz -C /opt \
    && ln -s /opt/node-v20.11.1-linux-arm64/bin/node /usr/local/bin/node \
    && rm -f /opt/node-v20.11.1-linux-arm64.tar.xz

# install golang 1.23.9
RUN wget https://golang.org/dl/go1.23.9.linux-arm64.tar.gz \
    && tar -C /usr/local -xzf go1.23.9.linux-arm64.tar.gz \
    && ln -s /usr/local/go/bin/go /usr/local/bin/go \
    && rm -f go1.23.9.linux-arm64.tar.gz

# run test
RUN go test -timeout 120s -v ./tests/integration_tests/...
