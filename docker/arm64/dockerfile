FROM python:3.10-slim-bookworm

# if you located in China, you can use ali<PERSON> mirror to speed up
# && echo "deb http://mirrors.aliyun.com/debian testing main" > /etc/apt/sources.list
RUN echo "deb http://deb.debian.org/debian testing main" > /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
       pkg-config \
       libseccomp-dev \
       wget \
       curl \
       xz-utils \
       zlib1g \
       expat \
       perl \
       libsqlite3-0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# copy main binary to /main
COPY main /main
# copy initial env
COPY env /env

# copy config file
COPY conf/config.yaml /conf/config.yaml
# copy python dependencies
COPY dependencies/python-requirements.txt /dependencies/python-requirements.txt
# copy entrypoint
COPY docker/entrypoint.sh /entrypoint.sh

RUN chmod +x /main /env /entrypoint.sh \
    && pip3 install --no-cache-dir httpx==0.27.2 requests==2.32.3 jinja2==3.1.6 PySocks httpx[socks] \
    && wget -O /opt/node-v20.11.1-linux-arm64.tar.xz https://npmmirror.com/mirrors/node/v20.11.1/node-v20.11.1-linux-arm64.tar.xz \
    && /env \
    && rm -f /env

ENV NODE_TAR_XZ=/opt/node-v20.11.1-linux-arm64.tar.xz
ENV NODE_DIR=/opt/node-v20.11.1-linux-arm64

ENTRYPOINT ["/entrypoint.sh"]
