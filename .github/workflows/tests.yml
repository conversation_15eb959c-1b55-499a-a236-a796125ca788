name: Run GoTest

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

concurrency:
  group: test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.arch == 'amd64' && 'ubuntu-latest' || 'arm64_runner' }}
    strategy:
      matrix:
        arch: [amd64, arm64]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build and run tests
        run: |
          if [ "${{ matrix.arch }}" == "amd64" ]; then
            docker build -t test -f docker/amd64-test/dockerfile .
            docker run --rm test
          elif [ "${{ matrix.arch }}" == "arm64" ]; then
            docker build -t test -f docker/arm64-test/dockerfile .
            docker run --rm test
          fi
