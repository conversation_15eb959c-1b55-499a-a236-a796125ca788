name: Build And Push Universal

on:
  push:
    branches:
      - main
  release:
    types: [published]

jobs:
  build-amd64:
    uses: ./.github/workflows/build.yml
    with:
      runner: ubuntu-latest
      arch: amd64
      arch_label: AMD64
    secrets:
      DOCKERHUB_USER: ${{ secrets.DOCKERHUB_USER }}
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}

  build-arm64:
    uses: ./.github/workflows/build.yml
    with:
      runner: arm64_runner
      arch: arm64
      arch_label: ARM64
    secrets:
      DOCKERHUB_USER: ${{ secrets.DOCKERHUB_USER }}
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}

  build-universal:
    needs: [build-amd64, build-arm64]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ vars.DIFY_SANDBOX_IMAGE_NAME || 'langgenius/dify-sandbox' }}
          tags: |
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=ref,event=branch
            type=sha,enable=true,priority=100,prefix=,suffix=,format=long
            type=raw,value=${{ github.ref_name }},enable=${{ startsWith(github.ref, 'refs/tags/') }}

      - name: Build Universal Docker Images
        run:
          for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n');
          do
            docker manifest create $tag $tag-amd64 $tag-arm64;
          done

      - name: Push Universal Docker Image
        run: 
          for tag in $(echo "${{ steps.meta.outputs.tags }}" | tr ',' '\n');
          do
            docker manifest push $tag;
          done
